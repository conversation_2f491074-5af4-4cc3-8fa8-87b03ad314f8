// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_local_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$homeLocalRepositoryHash() =>
    r'04cc6faa2c321419a913b212c86c30ab03065c59';

/// See also [HomeLocalRepository].
@ProviderFor(HomeLocalRepository)
final homeLocalRepositoryProvider =
    AutoDisposeNotifierProvider<HomeLocalRepository, List>.internal(
      HomeLocalRepository.new,
      name: r'homeLocalRepositoryProvider',
      debugGetCreateSourceHash:
          const bool.fromEnvironment('dart.vm.product')
              ? null
              : _$homeLocalRepositoryHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$HomeLocalRepository = AutoDisposeNotifier<List>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
