import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/theme/app_colors.dart';
import 'package:inventory_app_final/features/warehouse_management/view/widgets/edit_warehouse_dialog.dart';

class CustomWarehouseCard extends StatefulWidget {
  final String name;
  final String address;
  final String totalItemAmount;
  final String totalMoney;
  final String? imageUrl;

  const CustomWarehouseCard({
    super.key,
    required this.name,
    this.imageUrl,
    required this.address,
    required this.totalItemAmount,
    required this.totalMoney,
  });

  @override
  State<CustomWarehouseCard> createState() => _CustomWarehouseCardState();
}

class _CustomWarehouseCardState extends State<CustomWarehouseCard> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.w),
        color: Colors.white,
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Stack(
          children: [
            Row(
              children: [
                widget.imageUrl != null
                    ? SizedBox(
                      width: 65.w,
                      child: AspectRatio(
                        aspectRatio: 1,
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(10.w),
                          child: Image.asset(
                            // TODO 后期图片要从服务器获取
                            widget.imageUrl!,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    )
                    : SizedBox.shrink(),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        child: Row(
                          children: [
                            Icon(
                              Icons.warehouse_rounded,
                              color: AppColors.primaryColor,
                            ),
                            SizedBox(width: 5.w),
                            Expanded(
                              child: Text(
                                widget.name,
                                style: TextStyle(
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 5.h),
                      SizedBox(
                        child: Text(
                          "地址: ${widget.address}",
                          style: TextStyle(fontSize: 13.sp),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                      Text(
                        "物品总数: ${widget.totalItemAmount}",
                        style: TextStyle(fontSize: 13.sp),
                      ),
                      Text(
                        "库存金额: ${widget.totalMoney}",
                        style: TextStyle(fontSize: 13.sp),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Positioned(
              right: 1,
              bottom: 1,
              child: Row(
                children: [
                  // 删除按钮
                  GestureDetector(
                    onTap: () {
                      // TODO 检查仓库下是否有物品，如果有则不能删除
                    },
                    child: Container(
                      width: 20.w,
                      height: 20.h,
                      decoration: BoxDecoration(
                        color: Colors.red.shade300,
                        shape: BoxShape.circle,
                      ),
                      alignment: Alignment.center,
                      child: Icon(
                        Icons.delete_rounded,
                        color: Colors.white,
                        size: 15.sp,
                      ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                  // 编辑按钮
                  GestureDetector(
                    onTap: () {
                      showEditWarehouseDialog(
                        context: context,
                        initialName: widget.name,
                        initialAddress: widget.address,
                        onNameChanged: (_) {},
                        onConfirm: () {},
                        type: 0,
                      );
                    },
                    child: Container(
                      width: 20.w,
                      height: 20.h,
                      decoration: BoxDecoration(
                        color: AppColors.circleButtonColor,
                        shape: BoxShape.circle,
                      ),
                      alignment: Alignment.center,
                      child: Icon(
                        Icons.edit_rounded,
                        color: Colors.white,
                        size: 15.sp,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
