import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/config/app_routes.dart';
import 'package:inventory_app_final/core/theme/app_colors.dart';
import 'package:inventory_app_final/features/bottom_navigation_bar/bottom_navigation_bar.dart';

final RouteObserver<ModalRoute<void>> routeObserver =
    RouteObserver<ModalRoute<void>>();

void main() {
  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(360, 690),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (_, child) {
        return MaterialApp(
          title: 'Inventory App',
          navigatorObservers: [routeObserver],
          debugShowCheckedModeBanner: false,
          onGenerateRoute: AppRoutes.generateRoute,
          theme: ThemeData(
            primarySwatch: Colors.teal,
            scaffoldBackgroundColor: Color.fromARGB(255, 243, 245, 247),
            textSelectionTheme: TextSelectionThemeData(
              cursorColor: AppColors.secondaryColor, // ← 全局光标颜色
              selectionColor: AppColors.secondaryColor, // 选中背景色
              selectionHandleColor: AppColors.secondaryColor, // 拖动手柄颜色
            ),
          ),
          home: child,
        );
      },
      child: MyBottomNavigationBar(),
    );
  }
}
